import React, { useState, useCallback, useRef, useEffect } from 'react';
import ElementSelector from './ElementSelector';
import IntentDisplay from './IntentDisplay';
import EditConfirmation from './EditConfirmation';
import { intentApiService, Intent, SessionData, StreamingCallbacks } from '../services/intentApiService';
import '../styles/elementSelection.css';
import '../styles/intentDisplay.css';

interface IntentBasedEditorProps {
  prototypeId?: number;
  userId?: number;
  initialHtml: string;
  pageUrl: string;
  onHtmlChange: (html: string) => void;
  onError?: (error: string) => void;
  isCreateMode?: boolean; // New flag to skip database for create flow
}

interface EditResult {
  success: boolean;
  html?: string;
  error?: string;
  tokensUsed?: number;
  processingTime?: number;
}

interface StreamingProgress {
  stage: string;
  progress: number;
  message?: string;
}

const IntentBasedEditor: React.FC<IntentBasedEditorProps> = ({
  prototypeId,
  userId,
  initialHtml,
  pageUrl,
  onHtmlChange,
  onError,
  isCreateMode = false
}) => {
  // State management
  const [currentHtml, setCurrentHtml] = useState(initialHtml);
  const [session, setSession] = useState<SessionData | null>(null);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [currentIntent, setCurrentIntent] = useState<Intent | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);
  const [isProcessingEdit, setIsProcessingEdit] = useState(false);
  const [editResult, setEditResult] = useState<EditResult | null>(null);
  const [streamingProgress, setStreamingProgress] = useState<StreamingProgress | null>(null);
  const [showImplementationModal, setShowImplementationModal] = useState(false);

  // Mode management - default to user input mode
  const [inputMode, setInputMode] = useState<'user' | 'element'>('user');
  const [userInput, setUserInput] = useState('');

  // Refs
  const sessionInitialized = useRef(false);

  // Initialize session on component mount (skip for create mode)
  useEffect(() => {
    if (!sessionInitialized.current && !isCreateMode) {
      initializeSession();
      sessionInitialized.current = true;
    } else if (isCreateMode) {
      // For create mode, set a mock session to enable functionality
      setSession({
        id: 'create-mode-session',
        prototypeId: prototypeId || 0,
        userId: userId || 0,
        pageUrl,
        pageHtml: currentHtml,
        sessionState: 'active',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      } as SessionData);
      sessionInitialized.current = true;
    }
  }, [isCreateMode]);

  // Update HTML when it changes externally
  useEffect(() => {
    setCurrentHtml(initialHtml);
  }, [initialHtml]);

  /**
   * Initialize session for Readdy-style editing (only for edit mode)
   */
  const initializeSession = async () => {
    if (isCreateMode) return; // Skip for create mode

    try {
      const response = await intentApiService.createSession(
        prototypeId!,
        userId!,
        pageUrl,
        currentHtml
      );

      if (response.success && response.data) {
        setSession(response.data);
        console.log('Session initialized:', response.data.id);
      } else {
        onError?.('Failed to initialize editing session');
      }
    } catch (error) {
      console.error('Session initialization error:', error);
      onError?.('Failed to initialize editing session');
    }
  };

  /**
   * Handle user input submission
   */
  const handleUserInputSubmission = useCallback(async (input: string) => {
    if (!session) {
      onError?.('No active session. Please refresh the page.');
      return;
    }

    if (!input.trim()) {
      onError?.('Please enter a request.');
      return;
    }

    // Prevent duplicate calls
    if (isGeneratingIntent) {
      console.log('🔥 Intent generation already in progress, ignoring duplicate call');
      return;
    }

    // Immediate UI feedback
    setIsGeneratingIntent(true);
    setCurrentIntent(null);
    setEditResult(null);
    setUserInput(''); // Clear input after submission

    try {
      // Generate intent using session-based editing API
      const response = await fetch('/api/llm/v3/edit-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: session.id,
          userQuery: input.trim(),
          pageUrl: session.pageUrl
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let accumulatedHtml = '';
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        accumulatedHtml += chunk;

        // Update HTML in real-time
        setCurrentHtml(accumulatedHtml);
        onHtmlChange(accumulatedHtml);
      }

      // Mark as successful
      setEditResult({
        success: true,
        html: accumulatedHtml
      });

    } catch (error) {
      console.error('User input processing error:', error);
      setEditResult({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process request'
      });
      onError?.(error instanceof Error ? error.message : 'Failed to process request');
    } finally {
      setIsGeneratingIntent(false);
    }
  }, [session, isGeneratingIntent, onError, onHtmlChange]);

  /**
   * Handle element selection from ElementSelector
   */
  const handleElementSelection = useCallback(async (selector: string) => {
    if (!session) {
      onError?.('No active session. Please refresh the page.');
      return;
    }

    // Prevent duplicate calls
    if (isGeneratingIntent) {
      console.log('🔥 Intent generation already in progress, ignoring duplicate call');
      return;
    }

    // Immediate UI feedback
    setSelectedElement(selector);
    setIsGeneratingIntent(true);
    setCurrentIntent(null);
    setEditResult(null);

    try {
      // Extract element code from current HTML with fallback strategies
      const parser = new DOMParser();
      const doc = parser.parseFromString(currentHtml, 'text/html');
      let element = doc.querySelector(selector);
      let elementCode = '';

      if (!element) {
        console.log('🔍 Element not found with original selector:', selector);
        console.log('🔍 HTML preview:', currentHtml.substring(0, 300) + '...');
        console.log('🔍 Available elements:', doc.querySelectorAll('*').length);

        // Fallback Strategy 1: Try simplified selector
        const simplifiedSelector = selector.split(' ').pop() || selector; // Get last part
        console.log('🔍 Trying simplified selector:', simplifiedSelector);
        element = doc.querySelector(simplifiedSelector);

        if (!element) {
          // Fallback Strategy 2: Try by tag name if selector contains tag
          const tagMatch = selector.match(/^(\w+)/);
          if (tagMatch) {
            console.log('🔍 Trying tag-based selector:', tagMatch[1]);
            const elements = doc.querySelectorAll(tagMatch[1]);
            console.log(`🔍 Found ${elements.length} ${tagMatch[1]} elements`);
            element = elements[0]; // Take first matching tag
          }
        }

        if (!element) {
          // Fallback Strategy 3: Create a generic element for intent generation
          console.warn(`🔍 All strategies failed for selector: ${selector}, using generic element`);
          elementCode = `<div class="selected-element" data-original-selector="${selector}">Element content</div>`;
        } else {
          elementCode = element.outerHTML;
          console.log(`🔍 Found element using fallback strategy for selector: ${selector}`);
        }
      } else {
        elementCode = element.outerHTML;
        console.log('🔍 Found element with original selector:', selector);
      }

      // Generate intent using real API calls (no timeout)
      // console.log('🔥 Making real intent API call with:', { elementCode, selector });

      const response = await fetch('/api/llm/v3/generate-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          elementCode,
          htmlContent: currentHtml,
          elementSelector: selector
        })
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const result = await response.json();
        // console.log('🔥 Intent API response:', result);
        return {
          success: result.code === 'OK',
          data: result.data,
          error: result.error
        };
      });

      if (response.success && response.data) {
        setCurrentIntent(response.data);
      } else {
        onError?.(response.error?.message || 'Failed to generate intent');
      }
    } catch (error) {
      console.error('Intent generation error:', error);
      if (error.message === 'Intent generation timeout') {
        onError?.('Intent generation is taking too long. Please try again.');
      } else {
        onError?.('Failed to analyze element intent');
      }
    } finally {
      setIsGeneratingIntent(false);
    }
  }, [session, currentHtml, onError, isGeneratingIntent]);

  /**
   * Handle intent confirmation - show implementation options
   */
  const handleIntentConfirmation = useCallback(async (intent: Intent, userQuery?: string) => {
    if (!session) {
      onError?.('No active session. Please refresh the page.');
      return;
    }

    // Show implementation modal instead of directly implementing
    setShowImplementationModal(true);
  }, [session, onError]);

  /**
   * Handle implementation choice (inline/modal/page)
   */
  const handleImplementationChoice = useCallback(async (choice: 'inline' | 'modal' | 'page') => {
    if (!currentIntent || !session) return;

    setShowImplementationModal(false);
    setIsProcessingEdit(true);
    setEditResult(null);
    setStreamingProgress({ stage: 'initializing', progress: 0 });

    const streamingCallbacks: StreamingCallbacks = {
      onStart: (message) => {
        setStreamingProgress({ stage: 'starting', progress: 5, message });
      },
      onProgress: (stage, progress, message) => {
        setStreamingProgress({ stage, progress, message });
      },
      onData: (chunk) => {
        // Real-time HTML updates could be implemented here
        // For now, we'll wait for completion
      },
      onComplete: (result) => {
        setEditResult({
          success: true,
          html: result.html,
          tokensUsed: result.tokensUsed,
          processingTime: result.processingTime
        });
        setStreamingProgress(null);
        setIsProcessingEdit(false);

        // Update the HTML immediately to show changes
        if (result.html) {
          setCurrentHtml(result.html);
          onHtmlChange(result.html);
        }
      },
      onError: (error) => {
        setEditResult({
          success: false,
          error: error.message
        });
        setStreamingProgress(null);
        setIsProcessingEdit(false);
        onError?.(error.message);
      }
    };

    try {
      // Always use real API calls for implementation
      // console.log('🔥 Making real implementation API call');

      const response = await fetch('/api/llm/v3/implement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          htmlContent: currentHtml,
          elementText: selectedElement,
          elementType: 'element',
          implementationType: choice,
          conversationHistory: [],
          intentData: currentIntent
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Check if response is SSE (streaming) or JSON
      const contentType = response.headers.get('content-type');

      if (contentType?.includes('text/event-stream') || contentType?.includes('text/plain')) {
        // Handle SSE streaming response
        // console.log('🔥 Handling SSE streaming response');

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let finalResult = null;
        let allContent = '';

        if (reader) {
          streamingCallbacks.onStart?.('Starting implementation...');

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value, { stream: true });
              buffer += chunk;
              allContent += chunk;

              // console.log('🔥 SSE chunk received:', chunk.substring(0, 200) + '...');

              const lines = buffer.split('\n');
              buffer = lines.pop() || '';

              for (const line of lines) {
                // console.log('🔥 Processing SSE line:', line);

                if (line.startsWith('data: ')) {
                  const token = line.slice(6).trim();

                  // Handle LLM token streaming (not JSON events)
                  if (token && token !== '[DONE]' && token !== 'null') {
                    // This is a text token from LLM streaming
                    // Don't try to parse as JSON, just collect the text
                    // console.log('🔥 LLM token:', token);

                    // Update progress based on token count
                    const tokenCount = allContent.split(' ').length;
                    const progress = Math.min(90, 10 + (tokenCount / 10));
                    streamingCallbacks.onProgress?.('generating', progress, `Generating implementation... (${tokenCount} tokens)`);
                  }
                } else if (line.trim() && !line.startsWith('event:')) {
                  // Try to parse as direct JSON (fallback)
                  try {
                    const data = JSON.parse(line);
                    if (data.html || data.success) {
                      finalResult = data;
                      console.log('🔥 Final result from direct JSON:', finalResult);
                    }
                  } catch (e) {
                    // Not JSON, ignore
                  }
                }
              }
            }
          } catch (streamError) {
            console.error('🔥 SSE streaming error:', streamError);
            // Try to parse the entire content as JSON fallback
            try {
              const jsonResult = JSON.parse(allContent);
              if (jsonResult.html || jsonResult.success) {
                finalResult = jsonResult;
                console.log('🔥 Recovered result from full content:', finalResult);
              }
            } catch (e) {
              console.error('🔥 Failed to parse full content as JSON:', e);
            }
          }
        }

        // console.log('🔥 Final processing - finalResult:', finalResult);
        // console.log('🔥 All content received:', allContent.substring(0, 500) + '...');

        // Clean the content by removing SSE formatting
        const cleanContent = allContent
          .replace(/data:\s*/g, ' ')           // Remove "data:" prefixes
          .replace(/event:\s*\w+/g, '')        // Remove "event:" lines
          .replace(/\n\s*\n/g, '\n')           // Remove extra newlines
          .trim();

        // console.log('🔥 Cleaned content:', cleanContent.substring(0, 500) + '...');

        // Extract HTML from the cleaned LLM response
        let extractedHtml = '';

        // Try multiple patterns to find HTML in the response
        const htmlPatterns = [
          /```html\s*([\s\S]*?)\s*```/i,
          /<html[\s\S]*?<\/html>/i,
          /<body[\s\S]*?<\/body>/i,
          /<div[^>]*>[\s\S]*?<\/div>/i,
          /<[a-zA-Z][^>]*>[\s\S]*?<\/[a-zA-Z][^>]*>/i
        ];

        for (const pattern of htmlPatterns) {
          const match = cleanContent.match(pattern);
          if (match) {
            extractedHtml = match[1] || match[0];
            // console.log('🔥 Extracted HTML using pattern:', pattern.source);
            break;
          }
        }

        // If no structured HTML found, look for any HTML tags in clean content
        if (!extractedHtml) {
          const htmlStart = cleanContent.indexOf('<');
          const htmlEnd = cleanContent.lastIndexOf('>');

          if (htmlStart !== -1 && htmlEnd !== -1 && htmlEnd > htmlStart) {
            const potentialHtml = cleanContent.substring(htmlStart, htmlEnd + 1);
            // Verify it's actual HTML, not SSE data
            if (!potentialHtml.includes('event:') && !potentialHtml.includes('data:')) {
              extractedHtml = potentialHtml;
              // console.log('🔥 Extracted HTML from tag boundaries');
            }
          }
        }

        // Final cleanup of extracted HTML
        if (extractedHtml) {
          extractedHtml = extractedHtml
            .replace(/event:data\s*/g, '')       // Remove any remaining SSE artifacts
            .replace(/data:\s*/g, '')            // Remove data: prefixes
            .trim();
        }

        // console.log('🔥 Final extracted HTML:', extractedHtml.substring(0, 300) + '...');

        if (extractedHtml && extractedHtml.length > 50 && !extractedHtml.includes('event:')) {
          streamingCallbacks.onComplete?.({
            success: true,
            html: extractedHtml,
            tokensUsed: allContent.split(' ').length,
            processingTime: 1000
          });
        } else {
          // Fallback: Use original HTML - don't inject SSE data
          // console.log('🔥 Using fallback - original HTML preserved');
          streamingCallbacks.onComplete?.({
            success: true,
            html: currentHtml, // Keep original HTML clean
            tokensUsed: allContent.split(' ').length,
            processingTime: 1000
          });
        }
      } else {
        // Handle regular JSON response
        // console.log('🔥 Handling JSON response');
        const result = await response.json();

        if (result.success && result.html) {
          streamingCallbacks.onComplete?.({
            success: true,
            html: result.html,
            tokensUsed: result.tokensUsed || 500,
            processingTime: result.processingTime || 1000
          });
        } else {
          throw new Error(result.error || 'Implementation failed');
        }
      }
    } catch (error) {
      console.error('Intent implementation error:', error);
      setEditResult({
        success: false,
        error: error instanceof Error ? error.message : 'Implementation failed'
      });
      setStreamingProgress(null);
      setIsProcessingEdit(false);
    }
  }, [currentIntent, session, selectedElement, currentHtml, isCreateMode, onError]);

  /**
   * Handle accepting the implementation
   */
  const handleAcceptImplementation = useCallback(() => {
    // Changes are already applied, just clear the result
    setEditResult(null);
    setCurrentIntent(null);
    setSelectedElement(null);
  }, []);

  /**
   * Handle rejecting the implementation
   */
  const handleRejectImplementation = useCallback(() => {
    // Revert to original HTML
    if (editResult?.html) {
      // We don't have the original HTML stored, so we'll just clear the result
      // In a real implementation, you'd want to store the original HTML
      setEditResult(null);
      setCurrentIntent(null);
      setSelectedElement(null);
    }
  }, [editResult]);

  /**
   * Handle retrying the implementation
   */
  const handleRetryImplementation = useCallback(() => {
    if (currentIntent) {
      setEditResult(null);
      setShowImplementationModal(true);
    }
  }, [currentIntent]);

  /**
   * Handle intent rejection
   */
  const handleIntentRejection = useCallback(() => {
    setCurrentIntent(null);
    setSelectedElement(null);
    setEditResult(null);
  }, []);

  /**
   * Handle intent refinement
   */
  const handleIntentRefinement = useCallback(async (refinedQuery: string) => {
    if (!currentIntent) return;

    // Re-trigger intent generation with refined query
    // For now, we'll just update the intent suggestion
    setCurrentIntent({
      ...currentIntent,
      suggestion: refinedQuery
    });
  }, [currentIntent]);

  /**
   * Handle edit acceptance
   */
  const handleEditAcceptance = useCallback(() => {
    if (editResult?.success && editResult.html) {
      setCurrentHtml(editResult.html);
      onHtmlChange(editResult.html);

      // Reset state
      setCurrentIntent(null);
      setSelectedElement(null);
      setEditResult(null);
    }
  }, [editResult, onHtmlChange]);

  /**
   * Handle edit rejection (undo)
   */
  const handleEditRejection = useCallback(() => {
    setEditResult(null);
    setCurrentIntent(null);
    setSelectedElement(null);
  }, []);

  /**
   * Handle edit retry
   */
  const handleEditRetry = useCallback(() => {
    if (currentIntent) {
      setEditResult(null);
      handleIntentConfirmation(currentIntent);
    }
  }, [currentIntent, handleIntentConfirmation]);

  return (
    <div className="intent-based-editor">
      {/* Session Status */}
      {!session && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Initializing Session
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Setting up intelligent editing session...</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mode Toggle */}
      {session && !currentIntent && !isGeneratingIntent && (
        <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              How would you like to make changes?
            </h3>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setInputMode('user')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  inputMode === 'user'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Type Request
              </button>
              <button
                onClick={() => setInputMode('element')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  inputMode === 'element'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Click Element
              </button>
            </div>
          </div>

          {/* User Input Mode */}
          {inputMode === 'user' && (
            <div>
              <div className="mb-3">
                <p className="text-sm text-gray-600">
                  Describe what you'd like to add, modify, or improve on this page.
                </p>
              </div>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  if (userInput.trim() && !isGeneratingIntent) {
                    handleUserInputSubmission(userInput);
                  }
                }}
                className="space-y-3"
              >
                <textarea
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  placeholder="e.g., 'Add a contact form with name, email, and message fields' or 'Make the header sticky when scrolling'"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={3}
                  disabled={isGeneratingIntent}
                />
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={!userInput.trim() || isGeneratingIntent}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    {isGeneratingIntent ? (
                      <>
                        <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Processing...</span>
                      </>
                    ) : (
                      <span>Apply Changes</span>
                    )}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Element Selection Mode */}
          {inputMode === 'element' && (
            <div>
              <div className="mb-3">
                <p className="text-sm text-gray-600">
                  Click on any element in the preview below to see what we can do with it.
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Element Selector - Only show in element mode */}
      {session && inputMode === 'element' && (
        <ElementSelector
          html={currentHtml}
          onSelect={handleElementSelection}
        />
      )}

      {/* Intent Display */}
      <IntentDisplay
        intent={currentIntent}
        isLoading={isGeneratingIntent}
        onConfirm={handleIntentConfirmation}
        onReject={handleIntentRejection}
        onRefine={handleIntentRefinement}
      />

      {/* Edit Confirmation */}
      <EditConfirmation
        isProcessing={isProcessingEdit}
        result={editResult}
        onAccept={handleAcceptImplementation}
        onReject={handleRejectImplementation}
        onRetry={handleRetryImplementation}
        streamingProgress={streamingProgress}
      />

      {/* Implementation Choice Modal */}
      {showImplementationModal && currentIntent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Choose Implementation</h3>
                <button
                  onClick={() => setShowImplementationModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">How would you like to implement this feature?</h4>
                <div className="space-y-3">
                  {/* Inline Implementation */}
                  <button
                    onClick={() => handleImplementationChoice('inline')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Inline Functionality</h5>
                        <p className="text-sm text-gray-500">Add functionality directly to this element</p>
                      </div>
                    </div>
                  </button>

                  {/* Modal Implementation */}
                  <button
                    onClick={() => handleImplementationChoice('modal')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Modal/Popup</h5>
                        <p className="text-sm text-gray-500">Open functionality in a modal overlay</p>
                      </div>
                    </div>
                  </button>

                  {/* New Page Implementation */}
                  <button
                    onClick={() => handleImplementationChoice('page')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200">
                        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Create New Page</h5>
                        <p className="text-sm text-gray-500">Navigate to a dedicated page for this feature</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Debug Info (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-4 bg-gray-100 rounded-md text-xs">
          <h4 className="font-semibold mb-2">Debug Info:</h4>
          <p>Session ID: {session?.id || 'Not initialized'}</p>
          <p>Selected Element: {selectedElement || 'None'}</p>
          <p>Intent Generated: {currentIntent ? 'Yes' : 'No'}</p>
          <p>Processing: {isProcessingEdit ? 'Yes' : 'No'}</p>
          <p>Show Modal: {showImplementationModal ? 'Yes' : 'No'}</p>
        </div>
      )}
    </div>
  );
};

export default IntentBasedEditor;

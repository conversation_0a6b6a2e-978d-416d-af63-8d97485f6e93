const { pool } = require('./promptDbService');
const { v4: uuidv4 } = require('uuid');

/**
 * SessionService - Manages prototype sessions for Readdy-style intent generation
 *
 * This service handles session lifecycle management, storing HTML context once per session
 * to reduce token usage and improve performance. Sessions automatically expire after 24 hours.
 */

class SessionService {
  constructor() {
    this.defaultExpirationHours = 24;
    this.cleanupIntervalHours = 6;

    // Start automatic cleanup
    this.startAutomaticCleanup();
  }

  /**
   * Create a new prototype session
   * @param {Object} params
   * @param {number} params.prototype_id - ID of the prototype
   * @param {number} params.user_id - ID of the user
   * @param {string} params.page_url - Current page URL
   * @param {string} params.page_html - Full HTML content
   * @param {string} [params.session_state='active'] - Initial session state
   * @returns {Promise<Object>} Created session object
   */
  async createSession({ prototype_id, user_id, page_url, page_html, session_state = 'active' }) {
    console.log(`[SessionService] Creating session for user ${user_id}, prototype ${prototype_id}`);

    try {
      // Validate required parameters
      if (!prototype_id || !user_id || !page_url || !page_html) {
        throw new Error('Missing required parameters: prototype_id, user_id, page_url, page_html');
      }

      // Validate session state
      const validStates = ['active', 'editing', 'completed', 'expired'];
      if (!validStates.includes(session_state)) {
        throw new Error(`Invalid session state: ${session_state}. Must be one of: ${validStates.join(', ')}`);
      }

      // Check if user and prototype exist
      await this._validateUserAndPrototype(user_id, prototype_id);

      // Generate unique session ID
      const sessionId = uuidv4();
      const expiresAt = new Date(Date.now() + (this.defaultExpirationHours * 60 * 60 * 1000));

      const query = `
        INSERT INTO prototype_sessions (
          id, prototype_id, user_id, page_url, page_html,
          session_state, expires_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `;

      const values = [
        sessionId, prototype_id, user_id, page_url, page_html,
        session_state, expiresAt
      ];

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        throw new Error('Failed to create session');
      }

      console.log(`[SessionService] Session created successfully: ${sessionId}`);
      return result.rows[0];

    } catch (error) {
      console.error(`[SessionService] Error creating session:`, error);
      throw error;
    }
  }

  /**
   * Get session by ID
   * @param {string} sessionId - Session ID
   * @param {number} [userId] - Optional user ID for ownership validation
   * @returns {Promise<Object|null>} Session object or null if not found
   */
  async getSession(sessionId, userId = null) {
    console.log(`[SessionService] Getting session: ${sessionId}`);

    try {
      let query = 'SELECT * FROM prototype_sessions WHERE id = $1';
      let values = [sessionId];

      // Add user validation if provided
      if (userId) {
        query += ' AND user_id = $2';
        values.push(userId);
      }

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        console.log(`[SessionService] Session not found: ${sessionId}`);
        return null;
      }

      const session = result.rows[0];

      // Check if session is expired
      if (new Date() > new Date(session.expires_at)) {
        console.log(`[SessionService] Session expired: ${sessionId}`);
        await this.expireSession(sessionId);
        return null;
      }

      // Update last accessed time
      await this._updateLastAccessed(sessionId);

      return session;

    } catch (error) {
      console.error(`[SessionService] Error getting session:`, error);
      throw error;
    }
  }

  /**
   * Get active session for user and prototype
   * @param {number} userId - User ID
   * @param {number} prototypeId - Prototype ID
   * @returns {Promise<Object|null>} Active session or null
   */
  async getActiveSession(userId, prototypeId) {
    console.log(`[SessionService] Getting active session for user ${userId}, prototype ${prototypeId}`);

    try {
      const query = `
        SELECT * FROM prototype_sessions
        WHERE user_id = $1 AND prototype_id = $2 AND session_state = 'active'
        AND expires_at > CURRENT_TIMESTAMP
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const result = await pool.query(query, [userId, prototypeId]);

      if (result.rows.length === 0) {
        return null;
      }

      const session = result.rows[0];

      // Update last accessed time
      await this._updateLastAccessed(session.id);

      return session;

    } catch (error) {
      console.error(`[SessionService] Error getting active session:`, error);
      throw error;
    }
  }

  /**
   * Update session state
   * @param {string} sessionId - Session ID
   * @param {string} newState - New session state
   * @param {number} [userId] - Optional user ID for ownership validation
   * @returns {Promise<Object|null>} Updated session or null
   */
  async updateSessionState(sessionId, newState, userId = null) {
    console.log(`[SessionService] Updating session ${sessionId} state to: ${newState}`);

    try {
      // Validate session state
      const validStates = ['active', 'editing', 'completed', 'expired'];
      if (!validStates.includes(newState)) {
        throw new Error(`Invalid session state: ${newState}. Must be one of: ${validStates.join(', ')}`);
      }

      let query = `
        UPDATE prototype_sessions
        SET session_state = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      let values = [newState, sessionId];

      // Add user validation if provided
      if (userId) {
        query += ' AND user_id = $3';
        values.push(userId);
      }

      query += ' RETURNING *';

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        console.log(`[SessionService] Session not found or access denied: ${sessionId}`);
        return null;
      }

      console.log(`[SessionService] Session state updated successfully: ${sessionId}`);
      return result.rows[0];

    } catch (error) {
      console.error(`[SessionService] Error updating session state:`, error);
      throw error;
    }
  }

  /**
   * Expire a session
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} Success status
   */
  async expireSession(sessionId) {
    console.log(`[SessionService] Expiring session: ${sessionId}`);

    try {
      const result = await this.updateSessionState(sessionId, 'expired');
      return result !== null;
    } catch (error) {
      console.error(`[SessionService] Error expiring session:`, error);
      throw error;
    }
  }

  /**
   * Delete a session and all related data
   * @param {string} sessionId - Session ID
   * @param {number} [userId] - Optional user ID for ownership validation
   * @returns {Promise<boolean>} Success status
   */
  async deleteSession(sessionId, userId = null) {
    console.log(`[SessionService] Deleting session: ${sessionId}`);

    try {
      let query = 'DELETE FROM prototype_sessions WHERE id = $1';
      let values = [sessionId];

      // Add user validation if provided
      if (userId) {
        query += ' AND user_id = $2';
        values.push(userId);
      }

      const result = await pool.query(query, values);

      const success = result.rowCount > 0;
      if (success) {
        console.log(`[SessionService] Session deleted successfully: ${sessionId}`);
      } else {
        console.log(`[SessionService] Session not found or access denied: ${sessionId}`);
      }

      return success;

    } catch (error) {
      console.error(`[SessionService] Error deleting session:`, error);
      throw error;
    }
  }

  /**
   * Get sessions for a user
   * @param {number} userId - User ID
   * @param {Object} [options] - Query options
   * @param {number} [options.limit=10] - Maximum number of sessions to return
   * @param {string} [options.state] - Filter by session state
   * @returns {Promise<Array>} Array of sessions
   */
  async getUserSessions(userId, options = {}) {
    console.log(`[SessionService] Getting sessions for user: ${userId}`);

    try {
      const { limit = 10, state } = options;

      let query = `
        SELECT * FROM prototype_sessions
        WHERE user_id = $1
      `;
      let values = [userId];

      // Add state filter if provided
      if (state) {
        query += ' AND session_state = $2';
        values.push(state);
      }

      query += ' ORDER BY created_at DESC LIMIT $' + (values.length + 1);
      values.push(limit);

      const result = await pool.query(query, values);
      return result.rows;

    } catch (error) {
      console.error(`[SessionService] Error getting user sessions:`, error);
      throw error;
    }
  }

  /**
   * Clean up expired sessions
   * @param {Object} [options] - Cleanup options
   * @param {boolean} [options.force=false] - Force cleanup of all expired sessions
   * @returns {Promise<number>} Number of sessions cleaned up
   */
  async cleanupExpiredSessions(options = {}) {
    console.log(`[SessionService] Starting cleanup of expired sessions`);

    try {
      const { force = false } = options;

      let query = `
        DELETE FROM prototype_sessions
        WHERE expires_at < CURRENT_TIMESTAMP
      `;

      // If not forced, only clean up sessions that haven't been accessed recently
      if (!force) {
        query += ` OR (last_accessed < CURRENT_TIMESTAMP - INTERVAL '48 hours' AND session_state != 'active')`;
      }

      const result = await pool.query(query);

      console.log(`[SessionService] Cleaned up ${result.rowCount} expired sessions`);
      return result.rowCount;

    } catch (error) {
      console.error(`[SessionService] Error cleaning up expired sessions:`, error);
      throw error;
    }
  }

  /**
   * Get session statistics
   * @param {number} [userId] - Optional user ID to filter stats
   * @returns {Promise<Object>} Session statistics
   */
  async getSessionStats(userId = null) {
    console.log(`[SessionService] Getting session statistics${userId ? ` for user ${userId}` : ''}`);

    try {
      let query = `
        SELECT
          session_state,
          COUNT(*) as count,
          AVG(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - created_at))/3600) as avg_age_hours
        FROM prototype_sessions
      `;
      let values = [];

      if (userId) {
        query += ' WHERE user_id = $1';
        values.push(userId);
      }

      query += ' GROUP BY session_state';

      const result = await pool.query(query, values);

      // Transform results into a more usable format
      const stats = {
        total: 0,
        by_state: {},
        avg_age_hours: 0
      };

      let totalAgeSum = 0;
      let totalCount = 0;

      result.rows.forEach(row => {
        const count = parseInt(row.count);
        const avgAge = parseFloat(row.avg_age_hours) || 0;

        stats.by_state[row.session_state] = {
          count,
          avg_age_hours: avgAge
        };

        stats.total += count;
        totalAgeSum += (avgAge * count);
        totalCount += count;
      });

      stats.avg_age_hours = totalCount > 0 ? totalAgeSum / totalCount : 0;

      return stats;

    } catch (error) {
      console.error(`[SessionService] Error getting session statistics:`, error);
      throw error;
    }
  }

  /**
   * Start automatic cleanup process
   * @private
   */
  startAutomaticCleanup() {
    console.log(`[SessionService] Starting automatic cleanup every ${this.cleanupIntervalHours} hours`);

    // Run cleanup immediately
    this.cleanupExpiredSessions().catch(error => {
      console.error(`[SessionService] Initial cleanup failed:`, error);
    });

    // Schedule regular cleanup
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupExpiredSessions();
      } catch (error) {
        console.error(`[SessionService] Scheduled cleanup failed:`, error);
      }
    }, this.cleanupIntervalHours * 60 * 60 * 1000);
  }

  /**
   * Stop automatic cleanup process
   */
  stopAutomaticCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log(`[SessionService] Automatic cleanup stopped`);
    }
  }

  /**
   * Update last accessed time for a session
   * @private
   * @param {string} sessionId - Session ID
   */
  async _updateLastAccessed(sessionId) {
    try {
      await pool.query(
        'UPDATE prototype_sessions SET last_accessed = CURRENT_TIMESTAMP WHERE id = $1',
        [sessionId]
      );
    } catch (error) {
      // Log but don't throw - this is not critical
      console.warn(`[SessionService] Failed to update last accessed time for session ${sessionId}:`, error);
    }
  }

  /**
   * Validate that user and prototype exist
   * @private
   * @param {number} userId - User ID
   * @param {number} prototypeId - Prototype ID
   */
  async _validateUserAndPrototype(userId, prototypeId) {
    try {
      // Check if user exists
      const userResult = await pool.query('SELECT id FROM users WHERE id = $1', [userId]);
      if (userResult.rows.length === 0) {
        throw new Error(`User with ID ${userId} does not exist`);
      }

      // Check if prototype exists and belongs to user
      const prototypeResult = await pool.query(
        'SELECT id FROM prototypes WHERE id = $1 AND user_id = $2',
        [prototypeId, userId]
      );
      if (prototypeResult.rows.length === 0) {
        throw new Error(`Prototype with ID ${prototypeId} does not exist or does not belong to user ${userId}`);
      }

    } catch (error) {
      console.error(`[SessionService] Validation failed:`, error);
      throw error;
    }
  }
}

// Create and export singleton instance
const sessionService = new SessionService();

module.exports = sessionService;

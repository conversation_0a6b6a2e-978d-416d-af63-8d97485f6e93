// useElementSelection.ts
import { useEffect } from "react";

function getSelector(el: Element): string {
  if (!el) return "";

  // Strategy 1: Use ID if available (most reliable)
  if (el.id) {
    return `#${el.id}`;
  }

  // Strategy 2: Use unique class combination
  if (el.classList && el.classList.length > 0) {
    const classes = Array.from(el.classList)
      .filter(cls => !cls.includes('highlight')) // Exclude highlight classes
      .join('.');
    if (classes) {
      const selector = `${el.tagName.toLowerCase()}.${classes}`;
      // Test if this selector is unique
      const doc = el.ownerDocument;
      if (doc && doc.querySelectorAll(selector).length === 1) {
        return selector;
      }
    }
  }

  // Strategy 3: Use nth-child approach for uniqueness
  let path = [];
  let current = el;

  while (current && current.nodeType === Node.ELEMENT_NODE && current.tagName !== 'HTML') {
    let selector = current.tagName.toLowerCase();

    if (current.id) {
      selector += `#${current.id}`;
      path.unshift(selector);
      break; // ID is unique, we can stop here
    }

    // Add nth-child for uniqueness
    const parent = current.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children).filter(child =>
        child.tagName === current.tagName
      );
      if (siblings.length > 1) {
        const index = siblings.indexOf(current) + 1;
        selector += `:nth-child(${index})`;
      }
    }

    path.unshift(selector);
    current = parent as Element;
  }

  return path.join(' > ');
}

export default function useElementSelection(
  containerRef: React.RefObject<HTMLElement>,
  onSelect: (selector: string) => void
) {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    function handleMouseOver(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (!container || !container.contains(target)) return;
      target.classList.add("element-highlight");
    }

    function handleMouseOut(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (!container || !container.contains(target)) return;
      target.classList.remove("element-highlight");
    }

    function handleClick(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (!container || !container.contains(target)) return;

      // Debounce rapid clicks to prevent duplicate intent calls
      const now = Date.now();
      const lastClick = (target as any)._lastClickTime || 0;
      if (now - lastClick < 500) { // 500ms debounce
        console.log('🔥 Click debounced to prevent duplicate intent calls');
        return;
      }
      (target as any)._lastClickTime = now;

      e.preventDefault();
      e.stopPropagation();
      const selector = getSelector(target);
      onSelect(selector);
    }

    container.addEventListener("mouseover", handleMouseOver, true);
    container.addEventListener("mouseout", handleMouseOut, true);
    container.addEventListener("click", handleClick, true);

    return () => {
      container.removeEventListener("mouseover", handleMouseOver, true);
      container.removeEventListener("mouseout", handleMouseOut, true);
      container.removeEventListener("click", handleClick, true);
    };
  }, [containerRef, onSelect]);
}

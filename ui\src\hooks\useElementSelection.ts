// useElementSelection.ts
import { useEffect } from "react";

function getSelector(el: Element): string {
  if (!el) return "";
  let selector = el.tagName.toLowerCase();
  if (el.id) selector += `#${el.id}`;
  if (el.classList && el.classList.length)
    selector += "." + Array.from(el.classList).join(".");
  return selector;
}

export default function useElementSelection(
  containerRef: React.RefObject<HTMLElement>,
  onSelect: (selector: string) => void
) {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    function handleMouseOver(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (!container || !container.contains(target)) return;
      target.classList.add("element-highlight");
    }

    function handleMouseOut(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (!container || !container.contains(target)) return;
      target.classList.remove("element-highlight");
    }

    function handleClick(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (!container || !container.contains(target)) return;

      // Debounce rapid clicks to prevent duplicate intent calls
      const now = Date.now();
      const lastClick = (target as any)._lastClickTime || 0;
      if (now - lastClick < 500) { // 500ms debounce
        console.log('🔥 Click debounced to prevent duplicate intent calls');
        return;
      }
      (target as any)._lastClickTime = now;

      e.preventDefault();
      e.stopPropagation();
      const selector = getSelector(target);
      onSelect(selector);
    }

    container.addEventListener("mouseover", handleMouseOver, true);
    container.addEventListener("mouseout", handleMouseOut, true);
    container.addEventListener("click", handleClick, true);

    return () => {
      container.removeEventListener("mouseover", handleMouseOver, true);
      container.removeEventListener("mouseout", handleMouseOut, true);
      container.removeEventListener("click", handleClick, true);
    };
  }, [containerRef, onSelect]);
}

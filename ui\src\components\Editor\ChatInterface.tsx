/**
 * Production-ready Chat Interface Component
 * Handles conversation UI and message management
 */

import React, { useRef, useEffect, useState } from 'react';
import { FiSend, FiLoader } from 'react-icons/fi';
import { ChatMessage } from '../../hooks/useEditorV3';

// ============================================================================
// TYPES
// ============================================================================

interface ChatInterfaceProps {
  messages: ChatMessage[];
  input: string;
  isGenerating: boolean;
  onInputChange: (value: string) => void;
  onSubmit: (message: string) => void;
  onToggleSelector?: () => void;
  isSelectorActive?: boolean;
  className?: string;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatPlanContent = (content: string): string => {
  if (!content) return '';

  return content
    // Convert numbered sections to headers
    .replace(/^(\d+\.\s+)(.+)$/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1$2</h3>')
    // Convert bullet points to styled lists
    .replace(/^[\s]*[-•]\s+(.+)$/gm, '<li class="ml-4 mb-1 text-gray-700">$1</li>')
    // Wrap consecutive list items in ul tags
    .replace(/(<li[^>]*>.*<\/li>\s*)+/gs, '<ul class="space-y-1 mb-3">$&</ul>')
    // Convert bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
    // Convert paragraphs
    .replace(/^(?!<[hl]|<ul)(.+)$/gm, '<p class="mb-3 text-gray-700 leading-relaxed">$1</p>')
    // Clean up extra spacing
    .replace(/\n\s*\n/g, '\n')
    .trim();
};

const formatTimestamp = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// ============================================================================
// MESSAGE COMPONENTS
// ============================================================================

interface MessageProps {
  message: ChatMessage;
}

const UserMessage: React.FC<MessageProps> = ({ message }) => (
  <div className="flex justify-end mb-4">
    <div className="max-w-[80%] bg-blue-600 text-white rounded-2xl rounded-br-md px-4 py-3">
      <div className="text-sm leading-relaxed whitespace-pre-wrap">
        {message.content}
      </div>
      <div className="text-xs text-blue-100 mt-1 opacity-75">
        {formatTimestamp(message.timestamp)}
      </div>
    </div>
  </div>
);

const AssistantMessage: React.FC<MessageProps> = ({ message }) => (
  <div className="flex justify-start mb-4">
    <div className="max-w-[80%] bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm">
      {message.type === 'plan' ? (
        <div className="text-sm leading-relaxed">
          {/* Implementation Plan Header */}
          <div className="flex items-center space-x-2 mb-3">
            <div className="w-4 h-4 text-orange-500">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="font-medium text-gray-900">Implementation Plan</span>
          </div>

          {/* Plan Content */}
          <div className="text-gray-700 text-sm leading-relaxed">
            {message.content.split('\n').map((line, index) => {
              const trimmed = line.trim();
              if (!trimmed) return null;

              // Skip markdown headers and formatting
              if (trimmed.startsWith('#') || trimmed.startsWith('**') || trimmed.startsWith('📋') || trimmed.startsWith('🎯')) {
                return null;
              }

              // Handle bullet points
              if (trimmed.startsWith('•') || trimmed.startsWith('-')) {
                return (
                  <div key={index} className="ml-4 mb-1">
                    • {trimmed.replace(/^[•-]\s*/, '')}
                  </div>
                );
              }

              // Handle numbered sections (1. Layout, 2. Components, etc.)
              if (/^\d+\.\s/.test(trimmed)) {
                return (
                  <div key={index} className="font-medium text-gray-900 mt-3 mb-1">
                    {trimmed}
                  </div>
                );
              }

              // Handle section headers (Key Features:, Accessibility:, etc.)
              if (trimmed.endsWith(':') && trimmed.length < 30) {
                return (
                  <div key={index} className="font-medium text-gray-900 mt-3 mb-1">
                    {trimmed}
                  </div>
                );
              }

              // Regular text
              if (trimmed.length > 10) {
                return (
                  <div key={index} className="mb-2">
                    {trimmed}
                  </div>
                );
              }

              return null;
            }).filter(Boolean)}
          </div>
        </div>
      ) : (
        <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
          {message.content}
        </div>
      )}
      <div className="text-xs text-gray-400 mt-1">
        {formatTimestamp(message.timestamp)}
      </div>
    </div>
  </div>
);

const TypingIndicator: React.FC = () => (
  <div className="flex justify-start mb-4">
    <div className="bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm">
      <div className="flex items-center space-x-1">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <span className="text-xs text-gray-500 ml-2">AI is thinking...</span>
      </div>
    </div>
  </div>
);

// ============================================================================
// INPUT COMPONENT
// ============================================================================

interface ChatInputProps {
  value: string;
  isGenerating: boolean;
  onChange: (value: string) => void;
  onSubmit: () => void;
  onToggleSelector?: () => void;
  isSelectorActive?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  value,
  isGenerating,
  onChange,
  onSubmit,
  onToggleSelector,
  isSelectorActive
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() && !isGenerating) {
        onSubmit();
      }
    }
  };

  return (
    <div className="space-y-3">
      {/* Input Form */}
      <form
        onSubmit={(e) => {
          e.preventDefault();
          if (value.trim() && !isGenerating) {
            onSubmit();
          }
        }}
        className="relative"
      >
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={isGenerating ? "AI is working..." : "Tell me what to change, specific and clear. One task at a time.\nUse the Selector for better efficiency."}
          className="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-y min-h-[48px] max-h-[200px]"
          disabled={isGenerating}
          rows={1}
        />
        <button
          type="submit"
          disabled={!value.trim() || isGenerating}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
        >
          {isGenerating ? (
            <FiLoader className="w-4 h-4 animate-spin" />
          ) : (
            <FiSend className="w-4 h-4" />
          )}
        </button>
      </form>

      {/* Selector Button - Like Readdy (at bottom) */}
      {onToggleSelector && (
        <div className="flex justify-start">
          <button
            type="button"
            onClick={onToggleSelector}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              isSelectorActive
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
            </svg>
            <span>Selector</span>
          </button>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  input,
  isGenerating,
  onInputChange,
  onSubmit,
  onToggleSelector,
  isSelectorActive,
  className = ''
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Resizable messages area height - much smaller default
  const [messagesHeight, setMessagesHeight] = useState(180);
  const [isResizingHeight, setIsResizingHeight] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (!isGenerating) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isGenerating]);

  // Height resize handlers
  const handleMouseDownHeight = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizingHeight(true);
  };

  const handleMouseMoveHeight = (e: MouseEvent) => {
    if (!isResizingHeight) return;

    // Calculate new height based on mouse position
    const chatContainer = document.querySelector('.chat-container');
    if (chatContainer) {
      const rect = chatContainer.getBoundingClientRect();
      const newHeight = Math.max(200, Math.min(600, e.clientY - rect.top - 100)); // 100px for header
      setMessagesHeight(newHeight);
    }
  };

  const handleMouseUpHeight = () => {
    setIsResizingHeight(false);
  };

  // Add global mouse event listeners for height resizing
  useEffect(() => {
    if (isResizingHeight) {
      document.addEventListener('mousemove', handleMouseMoveHeight);
      document.addEventListener('mouseup', handleMouseUpHeight);
      document.body.style.cursor = 'row-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMoveHeight);
        document.removeEventListener('mouseup', handleMouseUpHeight);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizingHeight]);

  const handleSubmit = () => {
    if (input.trim() && !isGenerating) {
      onSubmit(input.trim());
    }
  };

  return (
    <div className={`flex flex-col h-full bg-gray-50 chat-container ${className}`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
        <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
        <p className="text-sm text-gray-600 mt-1">
          Describe what you'd like to create or modify
        </p>
      </div>

      {/* Resizable Messages Area */}
      <div
        className="overflow-y-auto px-6 py-4 space-y-4 flex-shrink-0"
        style={{ height: messagesHeight }}
      >
        {messages.map((message, index) => (
          <div key={index}>
            {message.role === 'user' ? (
              <UserMessage message={message} />
            ) : (
              <AssistantMessage message={message} />
            )}
          </div>
        ))}

        {isGenerating && <TypingIndicator />}

        <div ref={messagesEndRef} />
      </div>

      {/* Horizontal Resize Handle */}
      <div
        className="h-1 bg-gray-200 hover:bg-blue-400 cursor-row-resize transition-colors flex-shrink-0"
        onMouseDown={handleMouseDownHeight}
        title="Drag to resize messages area"
      />

      {/* Input Area - Fixed at bottom with proper height */}
      <div className="bg-white border-t border-gray-200 px-6 py-4 flex-shrink-0 min-h-[120px]">
        <ChatInput
          value={input}
          isGenerating={isGenerating}
          onChange={onInputChange}
          onSubmit={handleSubmit}
          onToggleSelector={onToggleSelector}
          isSelectorActive={isSelectorActive}
        />
      </div>
    </div>
  );
};

export default ChatInterface;

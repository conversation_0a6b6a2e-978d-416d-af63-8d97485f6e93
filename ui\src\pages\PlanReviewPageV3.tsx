import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>oa<PERSON>, FiArrowRight } from 'react-icons/fi';


/**
 * PlanReviewPageV3 - Readdy.ai style plan generation and review
 * Clean, focused plan view without conversation sidebar
 * Uses the same streaming approach as Readdy.ai
 */

export function PlanReviewPageV3() {
  const location = useLocation();
  const navigate = useNavigate();
  const { prompt, deviceType } = location.state || {};

  const [planData, setPlanData] = useState<any>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(true);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!prompt) {
      navigate('/prompt-v3');
      return;
    }
    generatePlan();
  }, [prompt, navigate]);

  const generatePlan = async () => {
    try {
      setIsGeneratingPlan(true);
      setError(null);
      setPlanData(null);

      // Generate plan using v3 API
      const response = await fetch('http://localhost:5000/api/llm/v3/plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, deviceType }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to generate plan: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.plan) {
        throw new Error('No plan data received');
      }

      // Store the full plan object for dynamic display
      setPlanData(result.plan);

    } catch (error: any) {
      console.error('Error generating plan:', error);
      setError(error.message || 'Failed to generate plan');

      // Show error without fallback content
      setPlanData(null);
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  const handleGenerate = async () => {
    setIsGeneratingCode(true);

    try {
      console.log('🚀 Navigating to V3 Refactored Editor with modal support');
      console.log('📋 Plan data:', planData);
      console.log('💬 Prompt:', prompt);

      // Navigate to the refactored editor with working modal functionality
      navigate('/editor-v3-refactored', {
        state: {
          prompt,
          deviceType,
          plan: JSON.stringify(planData), // Pass the plan data as string
          initialGeneration: true
        }
      });
    } catch (error) {
      console.error('Error:', error);
      setIsGeneratingCode(false);
    }
  };



  if (!prompt) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Design Plan</h1>
              <p className="text-gray-600 text-sm mt-1">Review the detailed implementation plan</p>
            </div>
            <div className="flex items-center space-x-3">
              <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-700">
                {deviceType === 'desktop' ? '🖥️ Desktop' : '📱 Mobile'}
              </span>
              <button
                onClick={handleGenerate}
                disabled={isGeneratingPlan || isGeneratingCode}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
              >
                {isGeneratingCode ? (
                  <>
                    <FiLoader className="animate-spin" />
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <span>Generate</span>
                    <FiArrowRight />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-6">
        {isGeneratingPlan ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Creating your design plan</h3>
              <p className="text-gray-600 text-sm">Analyzing your requirements and crafting a detailed implementation strategy...</p>
            </div>
          </div>
        ) : planData ? (
          <div className="space-y-6">
            {/* Project Overview */}
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-100">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Project Overview</h3>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {planData.overview || 'Design plan overview will appear here once generated.'}
                  </p>
                </div>
              </div>
            </div>

            {/* Implementation Sections */}
            {planData.sections && planData.sections.length > 0 && (
              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <span className="w-6 h-6 bg-purple-600 rounded-md flex items-center justify-center mr-2">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </span>
                  Implementation Sections
                </h3>
                <div className="space-y-4">
                  {planData.sections.map((section: any, index: number) => (
                    <div key={index} className="bg-white rounded-lg border border-gray-200 p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-md flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="text-base font-semibold text-gray-900 mb-1">{section.title}</h4>
                          {section.description && <p className="text-gray-600 mb-3 text-sm">{section.description}</p>}
                          {section.details && section.details.length > 0 && (
                            <div className="space-y-1">
                              {section.details.map((detail: string, detailIndex: number) => (
                                <div key={detailIndex} className="flex items-start space-x-2">
                                  <div className="w-1 h-1 bg-green-500 rounded-full flex-shrink-0 mt-2"></div>
                                  <span className="text-gray-700 text-sm leading-relaxed">{detail}</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Dynamic Additional Sections */}
            {Object.keys(planData).map((key) => {
              if (key === 'overview' || key === 'sections') return null;

              const sectionData = planData[key];
              if (!Array.isArray(sectionData) || sectionData.length === 0) return null;

              return (
                <div key={key} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-gray-900 mb-3 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</h3>
                      <div className="space-y-2">
                        {sectionData.map((item: string, itemIndex: number) => (
                          <div key={itemIndex} className="flex items-start space-x-2">
                            <div className="w-1 h-1 bg-green-500 rounded-full flex-shrink-0 mt-2"></div>
                            <span className="text-gray-700 text-sm leading-relaxed">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to generate plan</h3>
            <p className="text-gray-600 mb-4 text-sm">Something went wrong while creating your design plan.</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

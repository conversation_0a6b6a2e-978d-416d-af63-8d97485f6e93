import React, { useState } from 'react';
import { CheckCircleIcon, XCircleIcon, PencilIcon, SparklesIcon } from '@heroicons/react/24/outline';

interface Intent {
  id: string;
  userIntent: string;
  suggestion: string;
  confidence: number;
  canGenerate: boolean;
  estimatedTokens?: number;
}

interface IntentDisplayProps {
  intent: Intent | null;
  isLoading: boolean;
  onConfirm: (intent: Intent, userQuery?: string) => void;
  onReject: () => void;
  onRefine: (refinedQuery: string) => void;
}

const IntentDisplay: React.FC<IntentDisplayProps> = ({
  intent,
  isLoading,
  onConfirm,
  onReject,
  onRefine
}) => {
  const [isRefining, setIsRefining] = useState(false);
  const [refinedQuery, setRefinedQuery] = useState('');

  const handleRefineSubmit = () => {
    if (refinedQuery.trim()) {
      onRefine(refinedQuery.trim());
      setIsRefining(false);
      setRefinedQuery('');
    }
  };

  const handleConfirm = () => {
    if (intent) {
      onConfirm(intent, refinedQuery.trim() || undefined);
    }
  };

  if (isLoading) {
    return (
      <div className="intent-display loading">
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Analyzing your selection...</span>
        </div>
      </div>
    );
  }

  if (!intent) {
    return (
      <div className="intent-display empty">
        <div className="text-center p-8 text-gray-500">
          <SparklesIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>Click on an element to see what we can do with it</p>
        </div>
      </div>
    );
  }

  const confidenceColor = intent.confidence >= 0.8 ? 'text-green-600' : 
                         intent.confidence >= 0.6 ? 'text-yellow-600' : 'text-red-600';

  const confidenceLabel = intent.confidence >= 0.8 ? 'High' : 
                         intent.confidence >= 0.6 ? 'Medium' : 'Low';

  return (
    <div className="intent-display">
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Intent Analysis
            </h3>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-medium ${confidenceColor}`}>
                {confidenceLabel} Confidence
              </span>
              <span className="text-xs text-gray-500">
                ({Math.round(intent.confidence * 100)}%)
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4 space-y-4">
          {/* User Intent */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              What you're trying to do:
            </h4>
            <p className="text-gray-900 bg-blue-50 p-3 rounded-md border-l-4 border-blue-400">
              {intent.userIntent}
            </p>
          </div>

          {/* Suggestion */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Our suggestion:
            </h4>
            <p className="text-gray-900 bg-green-50 p-3 rounded-md border-l-4 border-green-400">
              {intent.suggestion}
            </p>
          </div>

          {/* Refinement Section */}
          {isRefining && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Refine your request:
              </h4>
              <div className="space-y-3">
                <textarea
                  value={refinedQuery}
                  onChange={(e) => setRefinedQuery(e.target.value)}
                  placeholder="Describe any specific changes or requirements..."
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={3}
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleRefineSubmit}
                    disabled={!refinedQuery.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    Apply Refinement
                  </button>
                  <button
                    onClick={() => {
                      setIsRefining(false);
                      setRefinedQuery('');
                    }}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Metadata */}
          {intent.estimatedTokens && (
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
              Estimated cost: ~{intent.estimatedTokens} tokens
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              {!isRefining && (
                <button
                  onClick={() => setIsRefining(true)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Refine
                </button>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={onReject}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <XCircleIcon className="h-4 w-4 mr-1" />
                Cancel
              </button>
              
              <button
                onClick={handleConfirm}
                disabled={!intent.canGenerate}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                {intent.canGenerate ? 'Generate Code' : 'Cannot Generate'}
              </button>
            </div>
          </div>
          
          {!intent.canGenerate && (
            <div className="mt-2 text-sm text-red-600">
              This element cannot be automatically enhanced. Try selecting a different element or refining your request.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IntentDisplay;

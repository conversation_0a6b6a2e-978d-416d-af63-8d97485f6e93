import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>Loader, FiMonitor, FiSmartphone, FiZap, FiTarget, FiLink } from 'react-icons/fi';

// CSS to hide textarea scrollbar
const textareaStyle = `
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
`;

/**
 * PromptInputPageV3 - Readdy.ai style initial prompt page
 * Sophisticated design matching StartingPage.png reference
 */

type DeviceType = 'desktop' | 'mobile';

export function PromptInputPageV3() {
  const [prompt, setPrompt] = useState('');
  const [deviceType, setDeviceType] = useState<DeviceType>('desktop');
  const [isGenerating, setIsGenerating] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isGenerating) return;

    setIsGenerating(true);

    try {
      // Navigate to plan review page with the prompt
      navigate('/plan-v3', {
        state: {
          prompt: prompt.trim(),
          deviceType
        }
      });
    } catch (error) {
      console.error('Error:', error);
      setIsGenerating(false);
    }
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
      <style>{textareaStyle}</style>
      <div className="flex-1 flex flex-col justify-center px-4 overflow-hidden">
        <div className="w-full max-w-3xl mx-auto">
          {/* Header */}
          <div className="text-center mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              What would you like to
              <span className="block text-blue-600">design today?</span>
            </h1>
          </div>

          {/* Prompt Input - Main Focus Area */}
          <form onSubmit={handleSubmit} className="mb-3">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe your prototype idea in detail...

For example: 'A modern landing page for a SaaS product with pricing section, testimonials, and contact form. Use a clean, professional design with blue and white colors.'"
                className="w-full h-40 px-6 py-4 text-gray-900 placeholder-gray-500 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 no-scrollbar text-base leading-relaxed"
                disabled={isGenerating}
              />

            {/* Device Selection and Submit */}
            <div className="flex items-center justify-between px-6 py-3 bg-gray-50 border-t border-gray-100">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 mr-2">Target:</span>
                <button
                  type="button"
                  onClick={() => setDeviceType('desktop')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    deviceType === 'desktop'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <FiMonitor className="inline mr-1" />
                  Desktop
                </button>
                <button
                  type="button"
                  onClick={() => setDeviceType('mobile')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    deviceType === 'mobile'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <FiSmartphone className="inline mr-1" />
                  Mobile
                </button>
              </div>

              <button
                type="submit"
                disabled={!prompt.trim() || isGenerating}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {isGenerating ? (
                  <>
                    <FiLoader className="animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <FiSend />
                    <span>Create</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>

        {/* Examples - Compact */}
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-1">Quick examples:</p>
          <div className="flex flex-wrap justify-center gap-1 max-w-4xl mx-auto">
            {[
              'Modern SaaS landing page with pricing and contact form',
              'E-commerce product page with shopping cart',
              'Dashboard with user profile and settings',
              'Portfolio website with project gallery'
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setPrompt(example)}
                className="px-2 py-1 text-xs text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors disabled:opacity-50"
                disabled={isGenerating}
              >
                {example}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
    </div>
  );
}

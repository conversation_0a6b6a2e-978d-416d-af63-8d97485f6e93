import React, { useRef, useEffect, useState } from 'react';
import { formatHTMLForEditor, needsFormatting } from '../utils/htmlFormatter';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  placeholder?: string;
  className?: string;
  autoFormat?: boolean; // Auto-format when content is loaded
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  language = 'html',
  placeholder = '<!-- Enter your HTML code here -->',
  className = '',
  autoFormat = true
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const lineNumbersRef = useRef<HTMLDivElement>(null);
  const [lineCount, setLineCount] = useState(1);

  // Update line numbers when content changes
  useEffect(() => {
    const lines = value.split('\n').length;
    setLineCount(lines);
  }, [value]);

  // Sync scroll between textarea and line numbers
  const handleScroll = () => {
    if (textareaRef.current && lineNumbersRef.current) {
      lineNumbersRef.current.scrollTop = textareaRef.current.scrollTop;
    }
  };

  // Handle tab key for proper indentation and auto-indentation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const textarea = e.currentTarget;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    if (e.key === 'Tab') {
      e.preventDefault();
      // Insert 2 spaces for tab
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);

      // Set cursor position after the inserted spaces
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    } else if (e.key === 'Enter') {
      // Auto-indentation on Enter
      const lines = value.substring(0, start).split('\n');
      const currentLine = lines[lines.length - 1];
      const indent = currentLine.match(/^(\s*)/)?.[1] || '';

      // Add extra indent if the current line ends with an opening tag
      const extraIndent = currentLine.trim().match(/<[^\/][^>]*[^\/]>$/) ? '  ' : '';

      const newValue = value.substring(0, start) + '\n' + indent + extraIndent + value.substring(end);
      onChange(newValue);

      // Set cursor position after the inserted indentation
      setTimeout(() => {
        const newPosition = start + 1 + indent.length + extraIndent.length;
        textarea.selectionStart = textarea.selectionEnd = newPosition;
      }, 0);

      e.preventDefault();
    }
  };

  // Generate line numbers
  const lineNumbers = Array.from({ length: lineCount }, (_, i) => i + 1);

  // Auto-format when content changes (if enabled and content needs formatting)
  useEffect(() => {
    if (autoFormat && value && needsFormatting(value) && (language === 'html' || !language)) {
      const formatted = formatHTMLForEditor(value);
      if (formatted !== value) {
        // Use setTimeout to avoid infinite loops
        setTimeout(() => onChange(formatted), 0);
      }
    }
  }, [value, autoFormat, language, onChange]);

  // Format button handler
  const handleFormat = () => {
    if (language === 'html' || !language) {
      const formatted = formatHTMLForEditor(value);
      onChange(formatted);
    }
  };

  return (
    <div className={`bg-gray-900 border border-gray-700 rounded-lg overflow-hidden ${className}`}>
      {/* Header with format button */}
      <div className="bg-gray-800 px-4 py-2 border-b border-gray-700 flex justify-between items-center">
        <span className="text-gray-400 text-sm font-medium">
          {language?.toUpperCase() || 'HTML'} EDITOR
        </span>
        <button
          onClick={handleFormat}
          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
          title="Format Code"
        >
          📐 Format
        </button>
      </div>

      {/* Editor Content */}
      <div className="relative flex">
      {/* Line Numbers */}
      <div
        ref={lineNumbersRef}
        className="flex flex-col bg-gray-800 text-gray-500 text-sm font-mono leading-6 py-4 px-3 select-none overflow-hidden border-r border-gray-700"
        style={{ minWidth: '60px' }}
      >
        {lineNumbers.map((lineNum) => (
          <div
            key={lineNum}
            className="text-right pr-2 h-6 flex items-center justify-end hover:text-gray-300 transition-colors"
            style={{ minHeight: '24px' }}
          >
            {lineNum}
          </div>
        ))}
      </div>

      {/* Code Input */}
      <div className="flex-1 relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onScroll={handleScroll}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          spellCheck={false}
          className="w-full h-full bg-transparent text-green-400 font-mono text-sm leading-6 py-4 px-4 resize-none focus:outline-none border-none placeholder-gray-500"
          style={{
            minHeight: '400px',
            lineHeight: '24px',
            tabSize: 2,
            fontFamily: '"Fira Code", "JetBrains Mono", "SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", monospace'
          }}
        />
      </div>
    </div>
  );
};

export default CodeEditor;

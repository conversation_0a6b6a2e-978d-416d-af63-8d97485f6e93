# JustProtoType

A prototyping tool for UX and BA teams that generates professional HTML/CSS prototypes from text prompts.

## Features

- Generate HTML/CSS prototypes from text prompts
- Real-time preview of generated prototypes
- Export HTML/CSS code
- Save and manage prototypes
- Modern UI with Tailwind CSS

## Tech Stack

- **Frontend**: React, Tailwind CSS
- **Backend**: Node.js, Express

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/JustProtoType.git
   cd JustProtoType
   ```

2. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

3. Install backend dependencies:
   ```bash
   cd ../backend
   npm install
   ```

### Running the Application

1. Start the backend server:
   ```bash
   cd backend
   npm run dev
   ```

2. Start the frontend development server:
   ```bash
   cd frontend
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5173`

## Usage

1. Enter a description of the UI you want to create in the prompt field
2. Click "Generate Prototype"
3. View the generated prototype in the preview section
4. Export the HTML/CSS code or save the prototype for later use

## License

This project is licensed under the MIT License - see the LICENSE file for details.

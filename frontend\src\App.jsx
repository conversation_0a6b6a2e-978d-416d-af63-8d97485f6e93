import { useState, useEffect } from 'react'
import './App.css'
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  Box,
  Paper,
  Typography,
  Button,
  useMediaQuery
} from '@mui/material'
import PrototypeContainer from './components/PrototypeContainer'
import ApiSettings from './components/ApiSettings'
import DesignerSettings from './components/DesignerSettings'
import TestHtmlRenderer from './components/TestHtmlRenderer'
import { llmService } from './services/llmService'

function App() {
  const [darkMode, setDarkMode] = useState(false)
  const [apiSettingsOpen, setApiSettingsOpen] = useState(false)
  const [designerSettingsOpen, setDesignerSettingsOpen] = useState(false)
  const [designSettings, setDesignSettings] = useState(llmService.getDesignSettings())
  const [apiError, setApiError] = useState('')

  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)')

  // Initialize dark mode based on user's system preference
  useEffect(() => {
    setDarkMode(prefersDarkMode)
  }, [prefersDarkMode])

  // Create a theme based on a purple/violet color scheme
  const theme = createTheme({
    palette: {
      mode: 'dark',
      primary: {
        main: '#A855F7', // Brighter violet/purple main color
        light: '#C084FC',
        dark: '#9333EA',
      },
      secondary: {
        main: '#F472B6', // Brighter pink color for accents
        light: '#F9A8D4',
        dark: '#EC4899',
      },
      background: {
        default: '#0F0F1A', // Darker background with slight purple tint
        paper: '#1A1A2E',
      },
      text: {
        primary: '#FFFFFF',
        secondary: '#D4D4D8',
      },
      divider: 'rgba(163, 163, 182, 0.2)',
      error: {
        main: '#F87171',
        light: '#FCA5A5',
        dark: '#EF4444',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontWeight: 800,
        letterSpacing: '-0.025em',
        fontSize: '2.5rem',
      },
      h2: {
        fontWeight: 700,
        letterSpacing: '-0.025em',
        fontSize: '2.2rem',
      },
      h3: {
        fontWeight: 700,
        letterSpacing: '-0.025em',
        fontSize: '1.8rem',
      },
      h4: {
        fontWeight: 700,
        letterSpacing: '-0.025em',
        fontSize: '1.5rem',
      },
      h5: {
        fontWeight: 700,
        letterSpacing: '-0.025em',
        fontSize: '1.3rem',
      },
      h6: {
        fontWeight: 700,
        letterSpacing: '-0.025em',
        fontSize: '1.1rem',
      },
      button: {
        fontWeight: 600,
        textTransform: 'none',
        letterSpacing: '-0.025em',
        fontSize: '0.95rem',
      },
      body1: {
        letterSpacing: '-0.01em',
        fontSize: '1rem',
      },
      body2: {
        letterSpacing: '-0.01em',
        fontSize: '0.95rem',
      },
      subtitle1: {
        fontWeight: 600,
        letterSpacing: '-0.015em',
        fontSize: '1.1rem',
      },
      subtitle2: {
        fontWeight: 600,
        letterSpacing: '-0.01em',
        fontSize: '1rem',
      },
    },
    shape: {
      borderRadius: 6,
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 4,
            padding: '8px 16px',
            boxShadow: 'none',
            fontWeight: 600,
            textTransform: 'none',
          },
          containedPrimary: {
            backgroundColor: '#3B82F6',
            '&:hover': {
              backgroundColor: '#2563EB',
              boxShadow: '0 4px 12px rgba(37, 99, 235, 0.4)',
            },
          },
          outlinedPrimary: {
            borderWidth: '1px',
            borderColor: 'rgba(59, 130, 246, 0.5)',
            '&:hover': {
              borderWidth: '1px',
              borderColor: '#3B82F6',
              backgroundColor: 'rgba(59, 130, 246, 0.08)',
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: 4,
            boxShadow: 'none',
            border: '1px solid rgba(75, 85, 99, 0.2)',
          },
          elevation1: {
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 4,
              backgroundColor: '#1F2937',
              '& fieldset': {
                borderColor: 'rgba(75, 85, 99, 0.4)',
              },
              '&:hover fieldset': {
                borderColor: 'rgba(59, 130, 246, 0.5)',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#3B82F6',
                borderWidth: '2px',
              },
            },
            '& .MuiInputLabel-root': {
              color: '#9CA3AF',
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 4,
            fontWeight: 500,
            backgroundColor: '#1F2937',
            color: '#D1D5DB',
            '&.MuiChip-outlined': {
              borderColor: 'rgba(75, 85, 99, 0.4)',
            },
          },
          outlinedPrimary: {
            borderColor: 'rgba(59, 130, 246, 0.5)',
            color: '#60A5FA',
          },
        },
      },
      MuiTab: {
        styleOverrides: {
          root: {
            fontWeight: 600,
            letterSpacing: '-0.01em',
            textTransform: 'none',
            minHeight: '48px',
            color: '#9CA3AF',
            '&.Mui-selected': {
              color: '#60A5FA',
            },
          },
        },
      },
      MuiDivider: {
        styleOverrides: {
          root: {
            borderColor: 'rgba(75, 85, 99, 0.2)',
          },
        },
      },
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            backgroundColor: '#030712',
            color: '#F9FAFB',
            scrollbarWidth: 'thin',
            scrollbarColor: '#4B5563 #1F2937',
            '&::-webkit-scrollbar': {
              width: '6px',
              height: '6px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#1F2937',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#4B5563',
              borderRadius: '3px',
            },
          },
        },
      },
      MuiIconButton: {
        styleOverrides: {
          root: {
            color: '#9CA3AF',
            '&:hover': {
              backgroundColor: 'rgba(75, 85, 99, 0.1)',
            },
          },
        },
      },
    },
  })

  // Initialize dark mode based on user's system preference
  useEffect(() => {
    setDarkMode(prefersDarkMode)
  }, [prefersDarkMode])

  const handleSaveDesignSettings = (newSettings) => {
    setDesignSettings(newSettings)
    llmService.setDesignSettings(newSettings)
    setDesignerSettingsOpen(false)
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          backgroundColor: theme.palette.background.default
        }}
        className="subtle-pattern"
      >
        {/* New Prototype Container */}
        <PrototypeContainer />

        {/* API Settings Dialog */}
        <ApiSettings
          open={apiSettingsOpen}
          onClose={() => setApiSettingsOpen(false)}
        />

        {/* Designer Settings Dialog */}
        <DesignerSettings
          open={designerSettingsOpen}
          onClose={() => setDesignerSettingsOpen(false)}
          settings={designSettings}
          onSave={handleSaveDesignSettings}
        />

        {/* API Error Snackbar */}
        {apiError && (
          <Box
            sx={{
              position: 'fixed',
              bottom: 20,
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 9999,
              maxWidth: '90%',
              width: 'auto',
            }}
          >
            <Paper
              elevation={6}
              sx={{
                backgroundColor: theme.palette.error.dark,
                color: '#fff',
                p: 2,
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
              }}
            >
              <Typography variant="body2" sx={{ mr: 2 }}>
                {apiError}
              </Typography>
              <Button
                variant="contained"
                color="inherit"
                size="small"
                onClick={() => setApiSettingsOpen(true)}
                sx={{
                  color: theme.palette.error.dark,
                  fontWeight: 600,
                  minWidth: 'auto',
                }}
              >
                Configure
              </Button>
            </Paper>
          </Box>
        )}
      </Box>
    </ThemeProvider>
  )
}

export default App
